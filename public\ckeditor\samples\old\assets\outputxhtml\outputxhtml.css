/*
 * Copyright (c) 2003-2018, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
 *
 * Styles used by the XHTML 1.1 sample page (xhtml.html).
 */

/**
 * Basic definitions for the editing area.
 */
body
{
	font-family: Arial, Verdana, sans-serif;
	font-size: 80%;
	color: #000000;
	background-color: #ffffff;
	padding: 5px;
	margin: 0px;
}

/**
 * Core styles.
 */

.Bold
{
	font-weight: bold;
}

.Italic
{
	font-style: italic;
}

.Underline
{
	text-decoration: underline;
}

.StrikeThrough
{
	text-decoration: line-through;
}

.Subscript
{
	vertical-align: sub;
	font-size: smaller;
}

.Superscript
{
	vertical-align: super;
	font-size: smaller;
}

/**
 * Font faces.
 */

.FontComic
{
	font-family: 'Comic Sans MS';
}

.FontCourier
{
	font-family: 'Courier New';
}

.FontTimes
{
	font-family: 'Times New Roman';
}

/**
 * Font sizes.
 */

.FontSmaller
{
	font-size: smaller;
}

.FontLarger
{
	font-size: larger;
}

.FontSmall
{
	font-size: 8pt;
}

.FontBig
{
	font-size: 14pt;
}

.FontDouble
{
	font-size: 200%;
}

/**
 * Font colors.
 */
.FontColor1
{
	color: #ff9900;
}

.FontColor2
{
	color: #0066cc;
}

.FontColor3
{
	color: #ff0000;
}

.FontColor1BG
{
	background-color: #ff9900;
}

.FontColor2BG
{
	background-color: #0066cc;
}

.FontColor3BG
{
	background-color: #ff0000;
}

/**
 * Indentation.
 */

.Indent1
{
	margin-left: 40px;
}

.Indent2
{
	margin-left: 80px;
}

.Indent3
{
	margin-left: 120px;
}

/**
 * Alignment.
 */

.JustifyLeft
{
	text-align: left;
}

.JustifyRight
{
	text-align: right;
}

.JustifyCenter
{
	text-align: center;
}

.JustifyFull
{
	text-align: justify;
}

/**
 * Other.
 */

code
{
	font-family: courier, monospace;
	background-color: #eeeeee;
	padding-left: 1px;
	padding-right: 1px;
	border: #c0c0c0 1px solid;
}

kbd
{
	padding: 0px 1px 0px 1px;
	border-width: 1px 2px 2px 1px;
	border-style: solid;
}

blockquote
{
	color: #808080;
}
