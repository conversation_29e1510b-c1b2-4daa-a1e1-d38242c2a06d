<?php

namespace App\Constants;

class ResMessages
{
    const RETRIEVED_SUCCESS = 'Data retrieved successfully.';
    const CREATED_SUCCESS = 'Data saved successfully.';
    const NOT_FOUND = 'Data not found.';
    const UPDATED_SUCCESS = 'Data updated successfully.';
    const DELETED_SUCCESS = 'Data deleted successfully.';
    const INVALID_CREDENTIALS = 'Invalid credentials.';
    const UNAUTHORIZED = 'Unauthorized access.';
    const FORBIDDEN = 'Forbidden access.';
    const VALIDATION_ERROR = 'Validation error.';
    const UNPROCESSABLE_ENTITY = 'Unprocessable entity.';
    const COMPANY_NOT_FOUND = 'Company not found.';
    const ALREADY_ACCEPTED = 'This customer has already been accepted another Registrar.';
    const ALREADY_ACCEPTED_YOU = 'You have already accepted this customer.';
}
