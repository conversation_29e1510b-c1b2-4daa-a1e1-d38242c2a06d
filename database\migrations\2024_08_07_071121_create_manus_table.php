<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::create('menus', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('company_id')->nullable()->constrained('companies', 'id');
            $table->string('access_code');
            $table->string('navigation_url')->nullable();
            $table->boolean('display_in_menu');
            $table->integer('parent_menu_id')->default(0)->nullable();
            $table->string('menu_icon')->nullable();
            $table->string('menu_class')->nullable();
            $table->integer('display_order');
            $table->boolean('is_active')->default(true);
            $table->softDeletes(); // adds deleted_at column
            $table->timestamps();
            $table->foreignId('created_by')->nullable()->constrained('users', 'id');
            $table->foreignId('updated_by')->nullable()->constrained('users', 'id');
        });

        // Seed the menus table with initial data
        DB::table('menus')->insert([
            [
                'id' => 1,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Dashboard',
                'access_code' => 'DASHBOARD',
                'navigation_url' => '/dashboard',
                'display_in_menu' => true,
                'parent_menu_id' => 0,
                'menu_icon' => 'mdi mdi-view-dashboard-outline',
                'menu_class' => 'nav-item',
                'display_order' => 1,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 2,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Settings',
                'access_code' => 'USERSMANAGEMENT',
                'navigation_url' => '',
                'display_in_menu' => true,
                'parent_menu_id' => 0,
                'menu_icon' => 'mdi mdi-account-group-outline',
                'menu_class' => 'nav-item',
                'display_order' => 2,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 3,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Users',
                'access_code' => 'USER',
                'navigation_url' => '/user',
                'display_in_menu' => true,
                'parent_menu_id' => 2,
                'menu_icon' => 'mdi mdi-account-plus-outline',
                'menu_class' => '',
                'display_order' => 1,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 4,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Role',
                'access_code' => 'ROLE',
                'navigation_url' => '/role',
                'display_in_menu' => true,
                'parent_menu_id' => 2,
                'menu_icon' => 'mdi mdi-briefcase-account-outline',
                'menu_class' => '',
                'display_order' => 2,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 5,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Menu',
                'access_code' => 'MENU',
                'navigation_url' => '/menu',
                'display_in_menu' => true,
                'parent_menu_id' => 2,
                'menu_icon' => 'mdi mdi-format-align-justify',
                'menu_class' => '',
                'display_order' => 3,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 6,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Menu Role Mapping',
                'access_code' => 'MENUROLEMAPPING',
                'navigation_url' => '/permission/{id}',
                'display_in_menu' => false,
                'parent_menu_id' => 2,
                'menu_icon' => '',
                'menu_class' => '',
                'display_order' => 4,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 7,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Manage Leaves',
                'access_code' => 'MANAGELEAVES',
                'navigation_url' => '',
                'display_in_menu' => true,
                'parent_menu_id' => 0,
                'menu_icon' => 'mdi mdi-format-list-bulleted-square',
                'menu_class' => '',
                'display_order' => 3,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 8,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Leaves Request',
                'access_code' => 'LEAVESREQUEST',
                'navigation_url' => '/leaves',
                'display_in_menu' => true,
                'parent_menu_id' => 7,
                'menu_icon' => 'mdi mdi-exit-run',
                'menu_class' => '',
                'display_order' => 1,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 9,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Leaves Setting',
                'access_code' => 'LEAVESSETTINGS',
                'navigation_url' => '/leave/setting',
                'display_in_menu' => true,
                'parent_menu_id' => 7,
                'menu_icon' => 'mdi mdi-cog-outline',
                'menu_class' => '',
                'display_order' => 3,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 10,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Leaves Report',
                'access_code' => 'LEAVESREPORT',
                'navigation_url' => '/leave/report',
                'display_in_menu' => true,
                'parent_menu_id' => 7,
                'menu_icon' => 'mdi mdi-file-document-outline',
                'menu_class' => '',
                'display_order' => 2,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 11,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Manage Attendance',
                'access_code' => 'MANAGEATTENDANCE',
                'navigation_url' => '',
                'display_in_menu' => true,
                'parent_menu_id' => 0,
                'menu_icon' => 'mdi mdi-calendar-month-outline',
                'menu_class' => '',
                'display_order' => 4,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 12,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Attendance Request',
                'access_code' => 'ATTENDANCEREQUEST',
                'navigation_url' => '/attendance/request',
                'display_in_menu' => true,
                'parent_menu_id' => 11,
                'menu_icon' => 'mdi mdi-calendar-multiselect-outline',
                'menu_class' => '',
                'display_order' => 1,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 13,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Attendance Report',
                'access_code' => 'ATTENDANCEREPORT',
                'navigation_url' => '/attendance/report',
                'display_in_menu' => true,
                'parent_menu_id' => 11,
                'menu_icon' => 'mdi mdi-calendar-cursor-outline',
                'menu_class' => '',
                'display_order' => 2,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 14,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Master',
                'access_code' => 'MASTER',
                'navigation_url' => null,
                'display_in_menu' => true,
                'parent_menu_id' => 0,
                'menu_icon' => 'mdi mdi-expand-all-outline',
                'menu_class' => '',
                'display_order' => 5,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 15,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Holidays',
                'access_code' => 'HOLIDAY',
                'navigation_url' => '/holidays',
                'display_in_menu' => true,
                'parent_menu_id' => 14,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 1,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 16,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Company',
                'access_code' => 'COMPANY',
                'navigation_url' => '/company',
                'display_in_menu' => true,
                'parent_menu_id' => 14,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 2,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 17,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Financial Year',
                'access_code' => 'FINANCIALYEAR',
                'navigation_url' => '/financial-year',
                'display_in_menu' => true,
                'parent_menu_id' => 14,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 3,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 18,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Allowance',
                'access_code' => 'ALLOWANCE',
                'navigation_url' => '/allowance',
                'display_in_menu' => true,
                'parent_menu_id' => 14,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 4,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 19,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Deduction',
                'access_code' => 'DEDUCTION',
                'navigation_url' => '/deduction',
                'display_in_menu' => true,
                'parent_menu_id' => 14,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 5,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 20,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'policies',
                'access_code' => 'POLICIESLIST',
                'navigation_url' => '/policy',
                'display_in_menu' => true,
                'parent_menu_id' => 14,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 6,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 21,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Department',
                'access_code' => 'DEPARTMENT',
                'navigation_url' => '/department',
                'display_in_menu' => true,
                'parent_menu_id' => 14,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 7,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 22,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Designation',
                'access_code' => 'DESIGNATION',
                'navigation_url' => '/designation',
                'display_in_menu' => true,
                'parent_menu_id' => 14,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 8,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 23,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Shift',
                'access_code' => 'SHIFT',
                'navigation_url' => '/shift',
                'display_in_menu' => true,
                'parent_menu_id' => 14,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 9,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 24,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Manage Projects',
                'access_code' => 'MANAGEPROJECTS',
                'navigation_url' => '',
                'display_in_menu' => true,
                'parent_menu_id' => 0,
                'menu_icon' => 'menu-icon mdi mdi-application-braces-outline',
                'menu_class' => '',
                'display_order' => 6,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 25,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Projects',
                'access_code' => 'PROJECTS',
                'navigation_url' => '/projects',
                'display_in_menu' => true,
                'parent_menu_id' => 24,
                'menu_icon' => 'menu-icon mdi mdi-application-braces-outline',
                'menu_class' => '',
                'display_order' => 1,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 26,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Tasks',
                'access_code' => 'TASKS',
                'navigation_url' => '/tasks',
                'display_in_menu' => true,
                'parent_menu_id' => 24,
                'menu_icon' => 'menu-icon mdi mdi-application-braces-outline',
                'menu_class' => '',
                'display_order' => 2,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 27,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Kanban',
                'access_code' => 'KANBAN',
                'navigation_url' => '/kanban/view',
                'display_in_menu' => true,
                'parent_menu_id' => 24,
                'menu_icon' => 'menu-icon mdi mdi-application-braces-outline',
                'menu_class' => '',
                'display_order' => 3,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 28,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Employees',
                'access_code' => 'EMPLOYEE',
                'navigation_url' => '',
                'display_in_menu' => true,
                'parent_menu_id' => 0,
                'menu_icon' => 'mdi mdi-briefcase-account-outline',
                'menu_class' => '',
                'display_order' => 7,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 29,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Employee List',
                'access_code' => 'EMPLOYEELIST',
                'navigation_url' => '/employees',
                'display_in_menu' => true,
                'parent_menu_id' => 28,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 1,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 30,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Employee Salary',
                'access_code' => 'EMPLOYEESALARY',
                'navigation_url' => '/employee-salary',
                'display_in_menu' => true,
                'parent_menu_id' => 28,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 2,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 31,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Clients',
                'access_code' => 'CLIENTS',
                'navigation_url' => '',
                'display_in_menu' => true,
                'parent_menu_id' => 0,
                'menu_icon' => 'mdi mdi-account-tie-outline',
                'menu_class' => '',
                'display_order' => 8,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 32,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Client List',
                'access_code' => 'CLIENTLIST',
                'navigation_url' => '/client',
                'display_in_menu' => true,
                'parent_menu_id' => 31,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 1,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 33,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'App Settings',
                'access_code' => 'APPSETTINGS',
                'navigation_url' => '',
                'display_in_menu' => true,
                'parent_menu_id' => 0,
                'menu_icon' => 'mdi mdi-cogs',
                'menu_class' => '',
                'display_order' => 9,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 34,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Email Settings',
                'access_code' => 'EMAILSETTINGS',
                'navigation_url' => '/email-settings',
                'display_in_menu' => true,
                'parent_menu_id' => 33,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 1,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
            [
                'id' => 35,
                'company_id' => env("APP_BASE_COMPANYID"),
                'name' => 'Notifications Settings',
                'access_code' => 'NOTIFICATIONSSETTING',
                'navigation_url' => '/notifications-settings',
                'display_in_menu' => true,
                'parent_menu_id' => 33,
                'menu_icon' => 'mdi mdi-chevron-double-right',
                'menu_class' => '',
                'display_order' => 2,
                'is_active' => true,
                'created_at' => now(),
                'created_by' => null,
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menus');
    }
};
