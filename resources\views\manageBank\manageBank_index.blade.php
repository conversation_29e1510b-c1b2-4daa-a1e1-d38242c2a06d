@extends('layouts.layout')
@section('content')
    <div class="container-fluid flex-grow-1 container-p-y">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="head-label text-center">
                    <h5 class="card-title mb-0"><b>{{ $menuName }}</b></h5>
                </div>
                @if ($permissions['canAdd'])
                    <button id="btnAdd" type="submit" class="btn btn-primary waves-effect waves-light"
                        onClick="fnAddEdit(this, '{{ url('manage-bank/create') }}', 0, 'Add Bank')">
                        <span class="tf-icons mdi mdi-plus">&nbsp;</span>Add Bank
                    </button>
                @endif
            </div>
            <hr class="my-0">
            <div class="card-datatable text-nowrap">
                <table id="grid" class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Action</th>
                            <th>Bank Name</th>
                            <th>Branch Name</th>
                            <th>Branch Manager Phone</th>
                            <th>Loan Manager Phone</th>
                            <th>IFSC Code</th>
                            <th>Branch Address</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        $(document).ready(function() {
            initializeDataTable();
        });

        function initializeDataTable() {
            $("#grid").DataTable({
                responsive: true,
                autoWidth: false,
                serverSide: false,
                processing: true,
                'language': {
                    "loadingRecords": "&nbsp;",
                    "processing": "<img src='{{ asset('assets/img/illustrations/loader.gif') }}' alt='loader' />"
                },
                order: [
                    [1, "desc"]
                ],
                ajax: {
                    url: "{{ config('apiConstants.MANAGE_BANK_URLS.MANAGE_BANK') }}",
                    type: "GET",
                    headers: {
                        Authorization: "Bearer " + getCookie("access_token"),
                    },
                },
                columns: [{
                        data: "id",
                        orderable: false,
                        render: function(data, type, row) {
                            var html = "<ul class='list-inline m-0'>";

                            // Edit Button (This is your existing edit button logic)
                            html += "<li class='list-inline-item'>" +
                                GetEditDeleteButton({{ $permissions['canEdit'] }},
                                    "{{ url('manage-bank/create') }}", "Edit",
                                    data, "Edit Bank") +
                                "</li>";

                            // Delete Button
                            html += "<li class='list-inline-item'>" +
                                GetEditDeleteButton({{ $permissions['canDelete'] }},
                                    "fnShowConfirmDeleteDialog('" + row.bank_name + "',fnDeleteRecord," +
                                    data + ",'" +
                                    '{{ config('apiConstants.MANAGE_BANK_URLS.MANAGE_BANK_DELETE') }}' +
                                    "','#grid')", "Delete") +
                                "</li>";

                            html += "</ul>";
                            return html;
                        },
                    },
                    {
                        data: "bank_name",
                    },
                    {
                        data: "branch_name",
                    },
                    {
                        data: "branch_manager_phone",
                    },
                    {
                        data: "loan_manager_phone",
                    },
                    {
                        data: "ifsc_code",
                    },
                    {
                        data: "address",
                    }
                ]
            });
        }
    </script>
@endsection
