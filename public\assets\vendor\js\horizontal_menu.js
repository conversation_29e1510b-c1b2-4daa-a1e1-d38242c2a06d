!function (e, t) { if ("object" == typeof exports && "object" == typeof module) module.exports = t(); else if ("function" == typeof define && define.amd) define([], t); else { var n = t(); for (var i in n) ("object" == typeof exports ? exports : e)[i] = n[i] } }(self, (function () { return function () { "use strict"; var e = { d: function (t, n) { for (var i in n) e.o(n, i) && !e.o(t, i) && Object.defineProperty(t, i, { enumerable: !0, get: n[i] }) }, o: function (e, t) { return Object.prototype.hasOwnProperty.call(e, t) }, r: function (e) { "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e, "__esModule", { value: !0 }) } }, t = {}; function n(e) { return n = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (e) { return typeof e } : function (e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, n(e) } function i(e) { return function (e) { if (Array.isArray(e)) return o(e) }(e) || function (e) { if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e) }(e) || function (e, t) { if (e) { if ("string" == typeof e) return o(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? o(e, t) : void 0 } }(e) || function () { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() } function o(e, t) { (null == t || t > e.length) && (t = e.length); for (var n = 0, i = new Array(t); n < t; n++)i[n] = e[n]; return i } function r(e, t) { for (var n = 0; n < t.length; n++) { var i = t[n]; i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, s(i.key), i) } } function s(e) { var t = function (e, t) { if ("object" != n(e) || !e) return e; var i = e[Symbol.toPrimitive]; if (void 0 !== i) { var o = i.call(e, "string"); if ("object" != n(o)) return o; throw new TypeError("@@toPrimitive must return a primitive value.") } return String(e) }(e); return "symbol" == n(t) ? t : String(t) } e.r(t), e.d(t, { Menu: function () { return a } }); var l = ["transitionend", "webkitTransitionEnd", "oTransitionEnd"], a = function () { function e(t) { var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, i = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : null; if (function (e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, e), this._el = t, this._horizontal = "horizontal" === n.orientation, this._animate = !1 !== n.animate, this._accordion = !1 !== n.accordion, this._showDropdownOnHover = Boolean(n.showDropdownOnHover), this._closeChildren = Boolean(n.closeChildren), this._rtl = "rtl" === document.documentElement.getAttribute("dir") || "rtl" === document.body.getAttribute("dir"), this._onOpen = n.onOpen || function () { }, this._onOpened = n.onOpened || function () { }, this._onClose = n.onClose || function () { }, this._onClosed = n.onClosed || function () { }, this._psScroll = null, this._topParent = null, this._menuBgClass = null, t.classList.add("menu"), t.classList[this._animate ? "remove" : "add"]("menu-no-animation"), this._horizontal) { t.classList.add("menu-horizontal"), t.classList.remove("menu-vertical"), this._inner = t.querySelector(".menu-inner"); var o = this._inner.parentNode; this._prevBtn = t.querySelector(".menu-horizontal-prev"), this._prevBtn || (this._prevBtn = document.createElement("a"), this._prevBtn.href = "#", this._prevBtn.className = "menu-horizontal-prev", o.appendChild(this._prevBtn)), this._wrapper = t.querySelector(".menu-horizontal-wrapper"), this._wrapper || (this._wrapper = document.createElement("div"), this._wrapper.className = "menu-horizontal-wrapper", this._wrapper.appendChild(this._inner), o.appendChild(this._wrapper)), this._nextBtn = t.querySelector(".menu-horizontal-next"), this._nextBtn || (this._nextBtn = document.createElement("a"), this._nextBtn.href = "#", this._nextBtn.className = "menu-horizontal-next", o.appendChild(this._nextBtn)), this._innerPosition = 0, this.update() } else { t.classList.add("menu-vertical"), t.classList.remove("menu-horizontal"); var r = i || window.PerfectScrollbar; r ? (this._scrollbar = new r(t.querySelector(".menu-inner"), { suppressScrollX: !0, wheelPropagation: !e._hasClass("layout-menu-fixed layout-menu-fixed-offcanvas") }), window.Helpers.menuPsScroll = this._scrollbar) : t.querySelector(".menu-inner").classList.add("overflow-auto") } for (var s = t.classList, l = 0; l < s.length; l++)s[l].startsWith("bg-") && (this._menuBgClass = s[l]); t.setAttribute("data-bg-class", this._menuBgClass), this._horizontal && window.innerWidth < window.Helpers.LAYOUT_BREAKPOINT && this.switchMenu("vertical"), this._bindEvents(), t.menuInstance = this } var t, n, o; return t = e, n = [{ key: "_bindEvents", value: function () { var t = this; this._evntElClick = function (n) { if (n.target.closest("ul") && n.target.closest("ul").classList.contains("menu-inner")) { var i = e._findParent(n.target, "menu-item", !1); i && (t._topParent = i.childNodes[0]) } var o = n.target.classList.contains("menu-toggle") ? n.target : e._findParent(n.target, "menu-toggle", !1); o && (n.preventDefault(), "true" !== o.getAttribute("data-hover") && t.toggle(o)) }, (!this._showDropdownOnHover && this._horizontal || !this._horizontal || window.Helpers.isMobileDevice) && this._el.addEventListener("click", this._evntElClick), this._evntWindowResize = function () { t.update(), t._lastWidth !== window.innerWidth && (t._lastWidth = window.innerWidth, t.update()); var e = document.querySelector("[data-template^='horizontal-menu']"); t._horizontal || e || t.manageScroll() }, window.addEventListener("resize", this._evntWindowResize), this._horizontal && (this._evntPrevBtnClick = function (e) { e.preventDefault(), t._prevBtn.classList.contains("disabled") || t._slide("prev") }, this._prevBtn.addEventListener("click", this._evntPrevBtnClick), this._evntNextBtnClick = function (e) { e.preventDefault(), t._nextBtn.classList.contains("disabled") || t._slide("next") }, this._nextBtn.addEventListener("click", this._evntNextBtnClick), this._evntBodyClick = function (e) { !t._inner.contains(e.target) && t._el.querySelectorAll(".menu-inner > .menu-item.open").length && t.closeAll() }, document.body.addEventListener("click", this._evntBodyClick), this._showDropdownOnHover && (this._evntElMouseOver = function (e) { if (e.target !== e.currentTarget && !e.target.parentNode.classList.contains("open")) { var n = e.target.classList.contains("menu-toggle") ? e.target : null; n && (e.preventDefault(), "true" !== n.getAttribute("data-hover") && t.toggle(n)) } e.stopPropagation() }, this._horizontal && window.screen.width > window.Helpers.LAYOUT_BREAKPOINT && this._el.addEventListener("mouseover", this._evntElMouseOver), this._evntElMouseOut = function (n) { var i = n.currentTarget, o = n.target, r = n.toElement || n.relatedTarget; if (o.closest("ul") && o.closest("ul").classList.contains("menu-inner") && (t._topParent = o), o !== i && (o.parentNode.classList.contains("open") || !o.classList.contains("menu-toggle")) && r && r.parentNode && !r.parentNode.classList.contains("menu-link")) { if (t._topParent && !e.childOf(r, t._topParent.parentNode)) { var s = t._topParent.classList.contains("menu-toggle") ? t._topParent : null; s && (n.preventDefault(), "true" !== s.getAttribute("data-hover") && (t.toggle(s), t._topParent = null)) } if (e.childOf(r, o.parentNode)) return; var l = o.classList.contains("menu-toggle") ? o : null; l && (n.preventDefault(), "true" !== l.getAttribute("data-hover") && t.toggle(l)) } n.stopPropagation() }, this._horizontal && window.screen.width > window.Helpers.LAYOUT_BREAKPOINT && this._el.addEventListener("mouseout", this._evntElMouseOut))) } }, { key: "_unbindEvents", value: function () { this._evntElClick && (this._el.removeEventListener("click", this._evntElClick), this._evntElClick = null), this._evntElMouseOver && (this._el.removeEventListener("mouseover", this._evntElMouseOver), this._evntElMouseOver = null), this._evntElMouseOut && (this._el.removeEventListener("mouseout", this._evntElMouseOut), this._evntElMouseOut = null), this._evntWindowResize && (window.removeEventListener("resize", this._evntWindowResize), this._evntWindowResize = null), this._evntBodyClick && (document.body.removeEventListener("click", this._evntBodyClick), this._evntBodyClick = null), this._evntInnerMousemove && (this._inner.removeEventListener("mousemove", this._evntInnerMousemove), this._evntInnerMousemove = null), this._evntInnerMouseleave && (this._inner.removeEventListener("mouseleave", this._evntInnerMouseleave), this._evntInnerMouseleave = null) } }, { key: "open", value: function (t) { var n = this, i = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this._closeChildren, o = this._findUnopenedParent(e._getItem(t, !0), i); if (o) { var r = e._getLink(o, !0); e._promisify(this._onOpen, this, o, r, e._findMenu(o)).then((function () { n._horizontal && e._isRoot(o) ? (n._toggleDropdown(!0, o, i), n._onOpened && n._onOpened(n, o, r, e._findMenu(o))) : n._animate && !n._horizontal ? (window.requestAnimationFrame((function () { return n._toggleAnimation(!0, o, !1) })), n._accordion && n._closeOther(o, i)) : n._animate ? (n._toggleDropdown(!0, o, i), n._onOpened && n._onOpened(n, o, r, e._findMenu(o))) : (o.classList.add("open"), n._onOpened && n._onOpened(n, o, r, e._findMenu(o)), n._accordion && n._closeOther(o, i)) })).catch((function () { })) } } }, { key: "close", value: function (t) { var n = this, i = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this._closeChildren, o = arguments.length > 2 && void 0 !== arguments[2] && arguments[2], r = e._getItem(t, !0), s = e._getLink(t, !0); r.classList.contains("open") && !r.classList.contains("disabled") && e._promisify(this._onClose, this, r, s, e._findMenu(r), o).then((function () { if (n._horizontal && e._isRoot(r)) n._toggleDropdown(!1, r, i), n._onClosed && n._onClosed(n, r, s, e._findMenu(r)); else if (n._animate && !n._horizontal) window.requestAnimationFrame((function () { return n._toggleAnimation(!1, r, i) })); else { if (r.classList.remove("open"), i) for (var t = r.querySelectorAll(".menu-item.open"), o = 0, l = t.length; o < l; o++)t[o].classList.remove("open"); n._onClosed && n._onClosed(n, r, s, e._findMenu(r)) } })).catch((function () { })) } }, { key: "_closeOther", value: function (t, n) { for (var i = e._findChild(t.parentNode, ["menu-item", "open"]), o = 0, r = i.length; o < r; o++)i[o] !== t && this.close(i[o], n) } }, { key: "toggle", value: function (t) { var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this._closeChildren, i = e._getItem(t, !0); i.classList.contains("open") ? this.close(i, n) : this.open(i, n) } }, { key: "_toggleDropdown", value: function (t, n, i) { var o = e._findMenu(n), r = n, s = !1; if (t) { e._findParent(n, "menu-sub", !1) && (s = !0, n = this._topParent ? this._topParent.parentNode : n); var l = Math.round(this._wrapper.getBoundingClientRect().width), a = this._innerPosition, u = this._getItemOffset(n), c = Math.round(n.getBoundingClientRect().width); u - 5 <= -1 * a ? this._innerPosition = -1 * u : u + a + c + 5 >= l && (this._innerPosition = c > l ? -1 * u : -1 * (u + c - l)), r.classList.add("open"); var d = Math.round(o.getBoundingClientRect().width); s ? u + this._innerPosition + 2 * d > l && d < l && d >= c && (o.style.left = [this._rtl ? "100%" : "-100%"]) : u + this._innerPosition + d > l && d < l && d > c && (o.style[this._rtl ? "marginRight" : "marginLeft"] = "-".concat(d - c, "px")), this._closeOther(r, i), this._updateSlider() } else { var h = e._findChild(n, ["menu-toggle"]); if (h.length && h[0].removeAttribute("data-hover", "true"), n.classList.remove("open"), o.style[this._rtl ? "marginRight" : "marginLeft"] = null, i) for (var _ = o.querySelectorAll(".menu-item.open"), v = 0, m = _.length; v < m; v++)_[v].classList.remove("open") } } }, { key: "_slide", value: function (e) { var t, n = Math.round(this._wrapper.getBoundingClientRect().width), i = this._innerWidth; "next" === e ? i + (t = this._getSlideNextPos()) < n && (t = n - i) : (t = this._getSlidePrevPos()) > 0 && (t = 0), this._innerPosition = t, this.update() } }, { key: "_getSlideNextPos", value: function () { for (var e = Math.round(this._wrapper.getBoundingClientRect().width), t = this._innerPosition, n = this._inner.childNodes[0], i = 0; n;) { if (n.tagName) { var o = Math.round(n.getBoundingClientRect().width); if (i + t - 5 <= e && i + t + o + 5 >= e) { o > e && i === -1 * t && (i += o); break } i += o } n = n.nextSibling } return -1 * i } }, { key: "_getSlidePrevPos", value: function () { for (var e = Math.round(this._wrapper.getBoundingClientRect().width), t = this._innerPosition, n = this._inner.childNodes[0], i = 0; n;) { if (n.tagName) { var o = Math.round(n.getBoundingClientRect().width); if (i - 5 <= -1 * t && i + o + 5 >= -1 * t) { o <= e && (i = i + o - e); break } i += o } n = n.nextSibling } return -1 * i } }, { key: "_findUnopenedParent", value: function (t, n) { for (var i = [], o = null; t;)t.classList.contains("disabled") ? (o = null, i = []) : (t.classList.contains("open") || (o = t), i.push(t)), t = e._findParent(t, "menu-item", !1); if (!o) return null; if (1 === i.length) return o; for (var r = 0, s = (i = i.slice(0, i.indexOf(o))).length; r < s; r++)if (i[r].classList.add("open"), this._accordion) for (var l = e._findChild(i[r].parentNode, ["menu-item", "open"]), a = 0, u = l.length; a < u; a++)if (l[a] !== i[r] && (l[a].classList.remove("open"), n)) for (var c = l[a].querySelectorAll(".menu-item.open"), d = 0, h = c.length; d < h; d++)c[d].classList.remove("open"); return o } }, { key: "_toggleAnimation", value: function (t, n, i) { var o = this, r = e._getLink(n, !0), s = e._findMenu(n); e._unbindAnimationEndEvent(n); var l = Math.round(r.getBoundingClientRect().height); n.style.overflow = "hidden"; var a = function () { n.classList.remove("menu-item-animating"), n.classList.remove("menu-item-closing"), n.style.overflow = null, n.style.height = null, o._horizontal || o.update() }; t ? (n.style.height = "".concat(l, "px"), n.classList.add("menu-item-animating"), n.classList.add("open"), e._bindAnimationEndEvent(n, (function () { a(), o._onOpened(o, n, r, s) })), setTimeout((function () { n.style.height = "".concat(l + Math.round(s.getBoundingClientRect().height), "px") }), 50)) : (n.style.height = "".concat(l + Math.round(s.getBoundingClientRect().height), "px"), n.classList.add("menu-item-animating"), n.classList.add("menu-item-closing"), e._bindAnimationEndEvent(n, (function () { if (n.classList.remove("open"), a(), i) for (var e = n.querySelectorAll(".menu-item.open"), t = 0, l = e.length; t < l; t++)e[t].classList.remove("open"); o._onClosed(o, n, r, s) })), setTimeout((function () { n.style.height = "".concat(l, "px") }), 50)) } }, { key: "_getItemOffset", value: function (e) { for (var t = this._inner.childNodes[0], n = 0; t !== e;)t.tagName && (n += Math.round(t.getBoundingClientRect().width)), t = t.nextSibling; return n } }, { key: "_updateSlider", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : null, t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null, n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : null, i = null !== e ? e : Math.round(this._wrapper.getBoundingClientRect().width), o = null !== t ? t : this._innerWidth, r = null !== n ? n : this._innerPosition; o < i || window.innerWidth < window.Helpers.LAYOUT_BREAKPOINT ? (this._prevBtn.classList.add("d-none"), this._nextBtn.classList.add("d-none")) : (this._prevBtn.classList.remove("d-none"), this._nextBtn.classList.remove("d-none")), o > i && window.innerWidth > window.Helpers.LAYOUT_BREAKPOINT && (0 === r ? this._prevBtn.classList.add("disabled") : this._prevBtn.classList.remove("disabled"), o + r <= i ? this._nextBtn.classList.add("disabled") : this._nextBtn.classList.remove("disabled")) } }, { key: "_innerWidth", get: function () { for (var e = this._inner.childNodes, t = 0, n = 0, i = e.length; n < i; n++)e[n].tagName && (t += Math.round(e[n].getBoundingClientRect().width)); return t } }, { key: "_innerPosition", get: function () { return parseInt(this._inner.style[this._rtl ? "marginRight" : "marginLeft"] || "0px", 10) }, set: function (e) { return this._inner.style[this._rtl ? "marginRight" : "marginLeft"] = "".concat(e, "px"), e } }, { key: "closeAll", value: function () { for (var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this._closeChildren, t = this._el.querySelectorAll(".menu-inner > .menu-item.open"), n = 0, i = t.length; n < i; n++)this.close(t[n], e) } }, { key: "update", value: function () { if (this._horizontal) { this.closeAll(); var e = Math.round(this._wrapper.getBoundingClientRect().width), t = this._innerWidth, n = this._innerPosition; e - n > t && ((n = e - t) > 0 && (n = 0), this._innerPosition = n), this._updateSlider(e, t, n) } else this._scrollbar && this._scrollbar.update() } }, { key: "manageScroll", value: function () { var e = window.PerfectScrollbar, t = document.querySelector(".menu-inner"); if (window.innerWidth < window.Helpers.LAYOUT_BREAKPOINT) null !== this._scrollbar && (this._scrollbar.destroy(), this._scrollbar = null), t.classList.add("overflow-auto"); else { if (null === this._scrollbar) { var n = new e(document.querySelector(".menu-inner"), { suppressScrollX: !0, wheelPropagation: !1 }); this._scrollbar = n } t.classList.remove("overflow-auto") } } }, { key: "switchMenu", value: function (e) { this._unbindEvents(); var t = document.querySelector("nav.layout-navbar"), n = document.querySelector("#navbar-collapse"), o = document.querySelector("#layout-menu div"), r = document.querySelector("#layout-menu"), s = document.querySelector(".menu-horizontal-wrapper"), l = document.querySelector(".menu-inner"), a = document.querySelector(".app-brand"), u = document.querySelector(".layout-menu-toggle"), c = document.querySelectorAll(".menu-inner .active"); if ("vertical" === e) { var d, h; this._horizontal = !1, o.insertBefore(a, s), o.insertBefore(l, s), o.classList.add("flex-column", "p-0"), (d = r.classList).remove.apply(d, i(r.classList)), (h = r.classList).add.apply(h, ["layout-menu", "menu", "menu-vertical"].concat([this._menuBgClass])), a.classList.remove("d-none", "d-lg-flex"), u.classList.remove("d-none"), l.classList.add("overflow-auto"); for (var _ = 0; _ < c.length - 1; ++_)c[_].classList.add("open") } else { var v, m; this._horizontal = !0, t.children[0].insertBefore(a, n), a.classList.add("d-none", "d-lg-flex"), s.appendChild(l), o.classList.remove("flex-column", "p-0"), (v = r.classList).remove.apply(v, i(r.classList)), (m = r.classList).add.apply(m, ["layout-menu-horizontal", "menu", "menu-horizontal", "container-fluid", "flex-grow-0"].concat([this._menuBgClass])), u.classList.add("d-none"), l.classList.remove("overflow-auto"); for (var f = 0; f < c.length; ++f)c[f].classList.remove("open") } this._bindEvents() } }, { key: "destroy", value: function () { if (this._el) { this._unbindEvents(); for (var t = this._el.querySelectorAll(".menu-item"), n = 0, i = t.length; n < i; n++)e._unbindAnimationEndEvent(t[n]), t[n].classList.remove("menu-item-animating"), t[n].classList.remove("open"), t[n].style.overflow = null, t[n].style.height = null; for (var o = this._el.querySelectorAll(".menu-menu"), r = 0, s = o.length; r < s; r++)o[r].style.marginRight = null, o[r].style.marginLeft = null; this._el.classList.remove("menu-no-animation"), this._wrapper && (this._prevBtn.parentNode.removeChild(this._prevBtn), this._nextBtn.parentNode.removeChild(this._nextBtn), this._wrapper.parentNode.insertBefore(this._inner, this._wrapper), this._wrapper.parentNode.removeChild(this._wrapper), this._inner.style.marginLeft = null, this._inner.style.marginRight = null), this._el.menuInstance = null, delete this._el.menuInstance, this._el = null, this._horizontal = null, this._animate = null, this._accordion = null, this._showDropdownOnHover = null, this._closeChildren = null, this._rtl = null, this._onOpen = null, this._onOpened = null, this._onClose = null, this._onClosed = null, this._scrollbar && (this._scrollbar.destroy(), this._scrollbar = null), this._inner = null, this._prevBtn = null, this._wrapper = null, this._nextBtn = null } } }], o = [{ key: "childOf", value: function (e, t) { if (e.parentNode) { for (; (e = e.parentNode) && e !== t;); return !!e } return !1 } }, { key: "_isRoot", value: function (t) { return !e._findParent(t, "menu-item", !1) } }, { key: "_findParent", value: function (e, t) { var n = !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2]; if ("BODY" === e.tagName.toUpperCase()) return null; for (e = e.parentNode; "BODY" !== e.tagName.toUpperCase() && !e.classList.contains(t);)e = e.parentNode; if (!(e = "BODY" !== e.tagName.toUpperCase() ? e : null) && n) throw new Error("Cannot find `.".concat(t, "` parent element")); return e } }, { key: "_findChild", value: function (e, t) { for (var n = e.childNodes, i = [], o = 0, r = n.length; o < r; o++)if (n[o].classList) { for (var s = 0, l = 0; l < t.length; l++)n[o].classList.contains(t[l]) && (s += 1); t.length === s && i.push(n[o]) } return i } }, { key: "_findMenu", value: function (e) { for (var t = e.childNodes[0], n = null; t && !n;)t.classList && t.classList.contains("menu-sub") && (n = t), t = t.nextSibling; if (!n) throw new Error("Cannot find `.menu-sub` element for the current `.menu-toggle`"); return n } }, { key: "_hasClass", value: function (e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : window.Helpers.ROOT_EL, n = !1; return e.split(" ").forEach((function (e) { t.classList.contains(e) && (n = !0) })), n } }, { key: "_getItem", value: function (t, n) { var i = null, o = n ? "menu-toggle" : "menu-link"; if (t.classList.contains("menu-item") ? e._findChild(t, [o]).length && (i = t) : t.classList.contains(o) && (i = t.parentNode.classList.contains("menu-item") ? t.parentNode : null), !i) throw new Error("".concat(n ? "Toggable " : "", "`.menu-item` element not found.")); return i } }, { key: "_getLink", value: function (t, n) { var i = [], o = n ? "menu-toggle" : "menu-link"; if (t.classList.contains(o) ? i = [t] : t.classList.contains("menu-item") && (i = e._findChild(t, [o])), !i.length) throw new Error("`".concat(o, "` element not found.")); return i[0] } }, { key: "_bindAnimationEndEvent", value: function (t, n) { var i = function (i) { i.target === t && (e._unbindAnimationEndEvent(t), n(i)) }, o = window.getComputedStyle(t).transitionDuration; o = parseFloat(o) * (-1 !== o.indexOf("ms") ? 1 : 1e3), t._menuAnimationEndEventCb = i, l.forEach((function (e) { return t.addEventListener(e, t._menuAnimationEndEventCb, !1) })), t._menuAnimationEndEventTimeout = setTimeout((function () { i({ target: t }) }), o + 50) } }, { key: "_promisify", value: function (e) { for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), i = 1; i < t; i++)n[i - 1] = arguments[i]; var o = e.apply(void 0, n); return o instanceof Promise ? o : !1 === o ? Promise.reject() : Promise.resolve() } }, { key: "_unbindAnimationEndEvent", value: function (e) { var t = e._menuAnimationEndEventCb; e._menuAnimationEndEventTimeout && (clearTimeout(e._menuAnimationEndEventTimeout), e._menuAnimationEndEventTimeout = null), t && (l.forEach((function (n) { return e.removeEventListener(n, t, !1) })), e._menuAnimationEndEventCb = null) } }, { key: "setDisabled", value: function (t, n) { e._getItem(t, !1).classList[n ? "add" : "remove"]("disabled") } }, { key: "isActive", value: function (t) { return e._getItem(t, !1).classList.contains("active") } }, { key: "isOpened", value: function (t) { return e._getItem(t, !1).classList.contains("open") } }, { key: "isDisabled", value: function (t) { return e._getItem(t, !1).classList.contains("disabled") } }], n && r(t.prototype, n), o && r(t, o), Object.defineProperty(t, "prototype", { writable: !1 }), e }(); return window.Menu = a, t }() }));