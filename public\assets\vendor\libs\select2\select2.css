.select2-container {
    margin: 0;
    width: 100% !important;
    display: inline-block;
    position: relative;
    vertical-align: middle;
    box-sizing: border-box
}

.select2-container .select2-selection--single {
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    height: 28px;
    user-select: none;
    -webkit-user-select: none
}

.select2-container .select2-selection--single .select2-selection__rendered {
    display: block;
    padding-left: 8px;
    padding-right: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.select2-container .select2-selection--single .select2-selection__clear {
    position: relative
}

.select2-container[dir=rtl] .select2-selection--single .select2-selection__rendered {
    padding-right: 8px;
    padding-left: 20px
}

.select2-container .select2-selection--multiple {
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    min-height: 32px;
    user-select: none;
    -webkit-user-select: none
}

.select2-container .select2-selection--multiple .select2-selection__rendered {
    display: inline-block;
    overflow: hidden;
    padding-left: 8px;
    text-overflow: ellipsis;
    white-space: nowrap
}

.select2-container .select2-search--inline {
    float: left
}

.select2-container .select2-search--inline .select2-search__field {
    box-sizing: border-box;
    border: none;
    font-size: 100%;
    margin-top: 5px;
    padding: 0
}

.select2-container .select2-search--inline .select2-search__field::-webkit-search-cancel-button {
    -webkit-appearance: none
}

.select2-dropdown {
    background-color: #fff;
    border: 1px solid #aaa;
    border-radius: 4px;
    box-sizing: border-box;
    display: block;
    position: absolute;
    left: -100000px;
    width: 100%;
    z-index: 1051
}

.select2-results {
    display: block
}

.select2-results__options {
    list-style: none;
    margin: 0;
    padding: 0
}

.select2-results__option {
    padding: 6px;
    user-select: none;
    -webkit-user-select: none
}

.select2-results__option[aria-selected] {
    cursor: pointer
}

.select2-container--open .select2-dropdown {
    left: 0
}

.select2-container--open .select2-dropdown--above {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.select2-container--open .select2-dropdown--below {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.select2-search--dropdown {
    display: block;
    padding: 4px
}

.select2-search--dropdown .select2-search__field {
    padding: 4px;
    width: 100%;
    box-sizing: border-box
}

.select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
    -webkit-appearance: none
}

.select2-search--dropdown.select2-search--hide {
    display: none
}

.select2-hidden-accessible {
    clip: rect(0 0 0 0) !important;
    overflow: hidden !important;
    position: absolute !important;
    padding: 0 !important;
    margin: -1px !important;
    border: 0 !important;
    height: 1px !important;
    width: 1px !important
}

.select2-close-mask {
    display: block;
    padding: 0;
    margin: 0;
    position: fixed;
    left: 0;
    top: 0;
    min-width: 100%;
    min-height: 100%;
    z-index: 99;
    width: auto;
    opacity: 0;
    border: 0;
    height: auto
}

.select2-container--default {
    padding: 0
}

.select2-container--default.select2-container--focus,
.select2-container--default.select2-container--open {
    padding: 0
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    padding-right: 2.25rem
}

.select2-container--default .select2-selection--single .select2-selection__clear {
    cursor: pointer;
    font-weight: 500;
    float: right
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    width: 2.25rem;
    position: absolute;
    right: 1px;
    top: 1px
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
    position: absolute;
    left: 50%;
    height: 0;
    width: 0;
    top: 50%;
    margin-top: -0.125rem;
    margin-left: -0.25rem;
    border-style: solid;
    border-width: .3125rem .25rem 0 .25rem
}

.select2-container--default,
.select2-container--default * {
    outline: 0 !important
}

.select2-container--default.select2-container--disabled {
    pointer-events: none
}

.select2-container--default.select2-container--disabled .select2-selection--single {
    cursor: not-allowed
}

.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__clear {
    display: none
}

.select2-container--default[dir=rtl] .select2-selection__clear {
    float: left
}

.select2-container--default[dir=rtl] .select2-selection__arrow {
    left: 1px;
    right: auto
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #888 rgba(0, 0, 0, 0);
    border-width: 0 .25rem .3125rem .25rem
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    margin: 0;
    box-sizing: border-box;
    display: block;
    list-style: none;
    width: 100%
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
    list-style: none
}

.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
    float: left
}

.select2-container--default .select2-selection--multiple .select2-selection__clear {
    cursor: pointer;
    font-weight: 500;
    float: right;
    margin-right: .625rem
}

.select2-container--default .select2-selection--multiple .select2-search--inline {
    line-height: 1.5rem
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    position: relative;
    border-radius: .25rem;
    padding: 0 .5rem;
    cursor: default;
    line-height: 1.5rem;
    float: left;
    font-size: .8125rem
}

html:not([dir=rtl]) .select2-container--default .select2-selection--multiple .select2-selection__choice {
    padding-right: 1rem
}

[dir=rtl] .select2-container--default .select2-selection--multiple .select2-selection__choice {
    padding-left: 1rem
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    position: absolute;
    font-weight: 500;
    color: inherit;
    display: inline-block;
    cursor: pointer;
    opacity: .5
}

html:not([dir=rtl]) .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    right: .3rem
}

[dir=rtl] .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    left: .3rem
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    opacity: .8;
    color: inherit
}

.select2-container--default.select2-container--disabled .select2-selection__choice__remove {
    display: none
}

.select2-container--default.select2-container--disabled .select2-selection--multiple {
    cursor: not-allowed
}

.select2-container--default[dir=rtl] .select2-selection__choice,
.select2-container--default[dir=rtl] .select2-selection__placeholder,
.select2-container--default[dir=rtl] .select2-search--inline {
    float: right
}

.select2-container--default[dir=rtl] .select2-selection__choice__remove {
    margin-left: 0;
    float: left;
    margin-right: .25rem
}

.select2-container--default[dir=rtl] .select2-selection__clear {
    margin-left: .625rem;
    float: left
}

.select2-container--default .select2-search__field::-moz-placeholder {
    opacity: 1
}

.select2-container--default .select2-search--inline .select2-search__field {
    box-shadow: none;
    background: rgba(0, 0, 0, 0);
    border: none;
    outline: 0;
    -webkit-appearance: textfield
}

.select2-container--default .select2-results>.select2-results__options {
    max-height: 12.5rem;
    overflow-y: auto
}

.select2-container--default .select2-results__option[role=group] {
    padding: 0
}

.select2-container--default .select2-results__option[aria-disabled=true] {
    color: #999
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__group {
    padding-left: 0
}

.select2-container--default.select2-container--open.select2-container--below .select2-selection {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.select2-container--default.select2-container--open.select2-container--above .select2-selection {
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.select2-container--default .select2-results__group {
    cursor: default;
    display: block;
    font-weight: 500
}

.form-floating.form-floating-select2 label {
    width: auto !important;
    height: auto !important;
    padding: 2px .375rem;
    margin-left: .625rem;
    margin-top: .125rem;
    transform: translateY(-0.8rem) translateX(-2px);
    font-size: .8125rem;
    opacity: 1
}

[dir=rtl] .form-floating.form-floating-select2 label {
    margin-right: .625rem;
    margin-left: 0px
}

.form-floating.form-floating-select2 label:after {
    content: "";
    position: absolute;
    height: 5px;
    width: 100%;
    left: 0;
    top: .525rem;
    z-index: -1
}

[dir=rtl] .select2-container--default .select2-selection--single .select2-selection__rendered {
    padding-left: 2.25rem !important
}

[dir=rtl] .modal-enable-otp~.select2-container--open .select2-dropdown {
    left: -1rem
}

.light-style .select2-selection--multiple .select2-selection__clear {
    margin-top: .375rem
}

.light-style .select2-selection--multiple .select2-selection__rendered {
    padding: 0
}

.light-style .select2-selection--multiple .select2-selection__choice {
    margin-right: .375rem;
    margin-top: .375rem
}

.light-style .select2-selection--multiple .select2-selection__placeholder {
    margin-top: .375rem
}

.light-style .select2-search__field {
    color: #433c50
}

.light-style .select2-dropdown {
    background: #fff;
    box-shadow: 0 .25rem .625rem rgba(46, 38, 61, .2);
    background-clip: padding-box;
    border-color: rgba(46, 38, 61, .05);
    z-index: 1000
}

.light-style .select2-container--default .select2-selection {
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    background-color: rgba(0, 0, 0, 0);
    border: 1px solid #d1cfd4;
    border-radius: .375rem
}

.light-style .select2-container--default .select2-selection:hover {
    border-color: #c0bec5
}

.light-style .select2-container--default .select2-selection__placeholder {
    color: #aba8b1
}

.light-style .select2-container--default .select2-selection--single {
    height: 3.0000625rem
}

.light-style .select2-container--default .select2-selection--single .select2-selection__clear {
    color: #aba8b1
}

.light-style .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 3.0000625rem;
    position: absolute
}

.light-style .select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #aba8b1 rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0)
}

.light-style .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: calc(2.2502625rem + 10px);
    color: #433c50;
    padding-left: 1rem
}

.light-style .select2-container--default[dir=rtl] .select2-selection--single .select2-selection__rendered {
    padding-right: 1rem
}

.light-style .select2-container--default.select2-container--disabled .select2-selection--single {
    background-color: rgba(0, 0, 0, 0);
    border-color: #e6e5e8 !important
}

.light-style .select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__rendered {
    color: #aba8b1
}

.light-style .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #aba8b1 rgba(0, 0, 0, 0)
}

.light-style .select2-container--default .select2-selection--multiple {
    min-height: 3.0000625rem;
    padding: calc(.8555rem - 1px - .375rem) 1rem
}

.light-style .select2-container--default .select2-selection--multiple .select2-selection__choice {
    color: #6d6777;
    background-color: #eeeeef
}

.light-style .select2-container--default.select2-container--focus .select2-selection--single .select2-selection__rendered,
.light-style .select2-container--default.select2-container--open .select2-selection--single .select2-selection__rendered {
    line-height: calc(2.2502625rem + 10px - 2px);
    padding-inline-start: calc(1rem - 1px);
    padding-inline-end: calc(2.25rem - 1px)
}

.light-style .select2-container--default.select2-container--focus .select2-selection--multiple,
.light-style .select2-container--default.select2-container--open .select2-selection--multiple {
    padding: calc(.8555rem - 1px - .375rem - 1px) calc(1rem - 1px)
}

.light-style .select2-container--default.select2-container--disabled .select2-selection--multiple {
    border-color: #e6e5e8 !important;
    background-color: rgba(0, 0, 0, 0)
}

.light-style .select2-container--default.select2-container--disabled .select2-selection--multiple .select2-selection__rendered {
    color: #aba8b1
}

.light-style .select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #d1cfd4
}

.light-style .select2-container--default .select2-search__field::-webkit-input-placeholder {
    color: #aba8b1
}

.light-style .select2-container--default .select2-search__field::-moz-placeholder {
    color: #aba8b1
}

.light-style .select2-container--default .select2-search__field:-ms-input-placeholder {
    color: #aba8b1
}

.light-style .select2-container--default .select2-results__option {
    padding-left: 1rem;
    padding-right: 1rem
}

.light-style .select2-container--default .select2-results__option[aria-selected=true] {
    background-color: rgba(46, 38, 61, .1)
}

.light-style .select2-container--default .select2-results__option .select2-results__option {
    width: calc(100% + 2rem);
    margin-left: -1rem;
    padding-left: 1rem
}

.light-style .select2-container--default .select2-results__option .select2-results__option .select2-results__option {
    padding-left: 2rem;
    margin-left: -1rem
}

.light-style .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-left: 3rem;
    margin-left: -2rem
}

.light-style .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-left: 4rem;
    margin-left: -3rem
}

.light-style .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-left: 5rem;
    margin-left: -4rem
}

.light-style .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-left: 1rem;
    margin-left: -5rem
}

.light-style .select2-container--default .select2-results__group {
    padding: .5rem .5rem
}

.light-style .select2-container--default .select2-results__option .select2-results__group {
    margin-left: -1rem
}

.light-style .is-invalid~.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: calc(2.2502625rem + 10px - 2px);
    padding-inline-start: calc(1rem - 1px);
    padding-inline-end: calc(2.25rem - 1px)
}

.light-style .is-invalid~.select2-container--default .select2-selection--multiple {
    padding: calc(.8555rem - 1px - .375rem - 1px) calc(1rem - 1px)
}

.light-style .form-floating .select2-container--default .select2-selection--single {
    height: 3.0000625rem
}

.light-style .form-floating .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 3.0000625rem
}

.light-style .form-floating .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 3.0000625rem
}

.light-style .form-floating .select2-container--default .select2-selection--multiple {
    min-height: 3.0000625rem;
    padding: calc(.8555rem - 1px - .375rem) 1rem
}

.light-style .form-floating .select2-container--default .select2-selection--multiple .select2-selection__rendered {
    padding: 0
}

.light-style .form-floating .select2-container--default.select2-container--focus .select2-selection--single .select2-selection__rendered,
.light-style .form-floating .select2-container--default.select2-container--open .select2-selection--single .select2-selection__rendered {
    line-height: calc(3.0000625rem - 2px);
    padding-inline-start: calc(1rem - 1px);
    padding-inline-end: calc(2.25rem - 1px)
}

.light-style .form-floating .select2-container--default.select2-container--focus .select2-selection--multiple,
.light-style .form-floating .select2-container--default.select2-container--open .select2-selection--multiple {
    padding: calc(.8555rem - 1px - .375rem - 1px) calc(1rem - 1px)
}

.light-style .form-floating .is-invalid~.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: calc(3.0000625rem - 2px);
    padding-inline-start: calc(1rem - 1px);
    padding-inline-end: calc(2.25rem - 1px)
}

.light-style .form-floating .is-invalid~.select2-container--default .select2-selection--multiple {
    padding: calc(.8555rem - 1px - .375rem - 1px) calc(1rem - 1px)
}

.light-style .select2-container--default[dir=rtl] .select2-selection--multiple .select2-selection__choice {
    margin-left: .375rem;
    margin-right: 0
}

.light-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__option {
    padding-right: 1rem;
    padding-left: 0 !important;
    margin-left: 0 !important;
    margin-right: -1rem
}

.light-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__option .select2-results__option {
    padding-right: 2rem;
    margin-right: -1rem
}

.light-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-right: 3rem;
    margin-right: -2rem
}

.light-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-right: 4rem;
    margin-right: -3rem
}

.light-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-right: 5rem;
    margin-right: -4rem
}

.light-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-right: 1rem;
    margin-right: -5rem
}

.light-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__group {
    margin-right: -1rem;
    margin-left: 0
}

.light-style .is-valid .select2-container--default .select2-selection,
.light-style .is-valid.select2-container--default .select2-selection {
    border-width: 2px;
    border-color: #56ca00 !important
}

.light-style .is-invalid .select2-container--default .select2-selection,
.light-style .is-invalid.select2-container--default .select2-selection {
    border-width: 2px;
    border-color: #ff4c51 !important
}

.light-style .select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #8a8d93 !important;
    color: #fff !important
}

.light-style .select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #56ca00 !important;
    color: #fff !important
}

.light-style .select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #16b1ff !important;
    color: #fff !important
}

.light-style .select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #ffb400 !important;
    color: #fff !important
}

.light-style .select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #ff4c51 !important;
    color: #fff !important
}

.light-style .select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #dfdfe3 !important;
    color: #2e263d !important
}

.light-style .select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #4b4b4b !important;
    color: #fff !important
}

.light-style .select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: rgba(46, 38, 61, .5) !important;
    color: #fff !important
}

.dark-style .select2-selection--multiple .select2-selection__choice {
    margin-top: .375rem;
    margin-right: .375rem
}

.dark-style .select2-selection--multiple .select2-selection__clear {
    margin-top: .375rem
}

.dark-style .select2-selection--multiple .select2-selection__placeholder {
    margin-top: .375rem
}

.dark-style .select2-selection--multiple .select2-selection__rendered {
    padding: 0
}

.dark-style .select2-container--default[dir=rtl] .select2-selection--multiple .select2-selection__choice {
    margin-left: .375rem;
    margin-right: 0
}

.dark-style .select2-container--default .select2-selection {
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    background-color: rgba(0, 0, 0, 0);
    border: 1px solid #595572;
    border-radius: .375rem
}

.dark-style .select2-container--default .select2-selection:hover {
    border-color: #686480
}

.dark-style .select2-container--default .select2-selection__placeholder {
    color: #7a7692
}

.dark-style .select2-container--default .select2-selection--single {
    height: 3.0000625rem
}

.dark-style .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 3.0000625rem;
    position: absolute
}

.dark-style .select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #7a7692 rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0)
}

.dark-style .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: calc(2.2502625rem + 10px);
    color: #d5d1ea;
    padding-left: 1rem
}

.dark-style .select2-container--default .select2-selection--single .select2-selection__clear {
    color: #7a7692
}

.dark-style .select2-container--default .select2-selection--multiple {
    min-height: 3.0000625rem;
    padding: calc(.8555rem - 1px - .375rem) 1rem
}

.dark-style .select2-container--default .select2-selection--multiple .select2-selection__choice {
    color: #b0acc7;
    background-color: #403c59
}

.dark-style .select2-container--default.select2-container--focus .select2-selection--single .select2-selection__rendered,
.dark-style .select2-container--default.select2-container--open .select2-selection--single .select2-selection__rendered {
    line-height: calc(2.2502625rem + 10px - 2px);
    padding-inline-start: calc(1rem - 1px);
    padding-inline-end: calc(2.25rem - 1px)
}

.dark-style .select2-container--default.select2-container--focus .select2-selection--multiple,
.dark-style .select2-container--default.select2-container--open .select2-selection--multiple {
    padding: calc(.8555rem - 1px - .375rem - 1px) calc(1rem - 1px)
}

.dark-style .select2-container--default.select2-container--disabled .select2-selection--multiple {
    border-color: #474360 !important;
    background-color: rgba(0, 0, 0, 0)
}

.dark-style .select2-container--default.select2-container--disabled .select2-selection--multiple .select2-selection__rendered {
    color: #7a7692
}

.dark-style .select2-container--default[dir=rtl] .select2-selection--single .select2-selection__rendered {
    padding-right: 1rem
}

.dark-style .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #7a7692 rgba(0, 0, 0, 0)
}

.dark-style .select2-container--default.select2-container--disabled .select2-selection--single {
    background-color: rgba(0, 0, 0, 0);
    border-color: #474360 !important
}

.dark-style .select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__rendered {
    color: #7a7692
}

.dark-style .select2-container--default .select2-search__field::-webkit-input-placeholder {
    color: #7a7692
}

.dark-style .select2-container--default .select2-search__field::-moz-placeholder {
    color: #7a7692
}

.dark-style .select2-container--default .select2-search__field:-ms-input-placeholder {
    color: #7a7692
}

.dark-style .select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #595572;
    background: rgba(0, 0, 0, 0)
}

.dark-style .select2-container--default .select2-results__option {
    padding-left: 1rem;
    padding-right: 1rem
}

.dark-style .select2-container--default .select2-results__option[aria-selected=true] {
    background-color: rgba(231, 227, 252, .1)
}

.dark-style .select2-container--default .select2-results__option .select2-results__option {
    padding-left: 1rem;
    margin-left: -1rem;
    width: calc(100% + 2rem)
}

.dark-style .select2-container--default .select2-results__option .select2-results__option .select2-results__option {
    padding-left: 2rem;
    margin-left: -1rem
}

.dark-style .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-left: 3rem;
    margin-left: -2rem
}

.dark-style .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-left: 4rem;
    margin-left: -3rem
}

.dark-style .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-left: 5rem;
    margin-left: -4rem
}

.dark-style .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-left: 1rem;
    margin-left: -5rem
}

.dark-style .select2-container--default .select2-results__option .select2-results__group {
    margin-left: -1rem
}

.dark-style .select2-container--default .select2-results__group {
    padding: .5rem .5rem
}

.dark-style .is-invalid~.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: calc(2.2502625rem + 10px - 2px);
    padding-inline-start: calc(1rem - 1px);
    padding-inline-end: calc(2.25rem - 1px)
}

.dark-style .is-invalid~.select2-container--default .select2-selection--multiple {
    padding: calc(.8555rem - 1px - .375rem - 1px) calc(1rem - 1px)
}

.dark-style .form-floating .select2-container--default .select2-selection--single {
    height: 3.0000625rem
}

.dark-style .form-floating .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 3.0000625rem
}

.dark-style .form-floating .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 3.0000625rem
}

.dark-style .form-floating .select2-container--default .select2-selection--multiple {
    min-height: 3.0000625rem;
    padding: calc(.8555rem - 1px - .375rem) 1rem
}

.dark-style .form-floating .select2-container--default .select2-selection--multiple .select2-selection__rendered {
    padding: 0
}

.dark-style .form-floating .select2-container--default.select2-container--focus .select2-selection--single .select2-selection__rendered,
.dark-style .form-floating .select2-container--default.select2-container--open .select2-selection--single .select2-selection__rendered {
    line-height: calc(3.0000625rem - 2px);
    padding-inline-start: calc(1rem - 1px);
    padding-inline-end: calc(2.25rem - 1px)
}

.dark-style .form-floating .select2-container--default.select2-container--focus .select2-selection--multiple,
.dark-style .form-floating .select2-container--default.select2-container--open .select2-selection--multiple {
    padding: calc(.8555rem - 1px - .375rem - 1px) calc(1rem - 1px)
}

.dark-style .form-floating .is-invalid~.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: calc(3.0000625rem - 2px);
    padding-inline-start: calc(1rem - 1px);
    padding-inline-end: calc(2.25rem - 1px)
}

.dark-style .form-floating .is-invalid~.select2-container--default .select2-selection--multiple {
    padding: calc(.8555rem - 1px - .375rem - 1px) calc(1rem - 1px)
}

.dark-style .select2-dropdown {
    z-index: 1000;
    background: #312d4b;
    border-color: rgba(255, 255, 255, .05);
    background-clip: padding-box;
    box-shadow: 0 .125rem .625rem 0 rgba(49, 45, 75, .4)
}

.dark-style .select2-search__field {
    color: #d5d1ea
}

.dark-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__option {
    padding-left: 0 !important;
    padding-right: 1rem;
    margin-left: 0 !important;
    margin-right: -1rem
}

.dark-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__option .select2-results__option {
    padding-right: 2rem;
    margin-right: -1rem
}

.dark-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-right: 3rem;
    margin-right: -2rem
}

.dark-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-right: 4rem;
    margin-right: -3rem
}

.dark-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-right: 5rem;
    margin-right: -4rem
}

.dark-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    padding-right: 1rem;
    margin-right: -5rem
}

.dark-style[dir=rtl] .select2-container--default .select2-results__option .select2-results__group {
    margin-left: 0;
    margin-right: -1rem
}

.dark-style .is-valid .select2-container--default .select2-selection,
.dark-style .is-valid.select2-container--default .select2-selection {
    border-width: 2px;
    border-color: #56ca00 !important
}

.dark-style .is-invalid .select2-container--default .select2-selection,
.dark-style .is-invalid.select2-container--default .select2-selection {
    border-width: 2px;
    border-color: #ff4c51 !important
}

.dark-style .select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #8a8d93 !important;
    color: #fff !important
}

.dark-style .select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #56ca00 !important;
    color: #fff !important
}

.dark-style .select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #16b1ff !important;
    color: #fff !important
}

.dark-style .select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #ffb400 !important;
    color: #fff !important
}

.dark-style .select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #ff4c51 !important;
    color: #fff !important
}

.dark-style .select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #46445b !important;
    color: #fff !important
}

.dark-style .select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #d7d5ec !important;
    color: #2e263d !important
}

.dark-style .select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: rgba(231, 227, 252, .1) !important;
    color: #2e263d !important
}