body,
.bg-body {
    background: #f4f5fa;
}

/* .bg-body {
  background: #f4f5fa !important;
} */

.sk-primary.sk-plane,
.sk-primary .sk-chase-dot:before,
.sk-primary .sk-bounce-dot,
.sk-primary .sk-wave-rect,
.sk-primary.sk-pulse,
.sk-primary .sk-swing-dot,
.sk-primary .sk-circle-dot:before,
.sk-primary .sk-circle-fade-dot:before,
.sk-primary .sk-grid-cube,
.sk-primary .sk-fold-cube:before {
    background-color: #29a8df;
}

.text-primary {
    color: #29a8df !important;
}

.text-body[href]:hover {
    color: #29a8df !important;
}

.bg-primary {
    background-color: #29a8df !important;
}

a.bg-primary:hover,
a.bg-primary:focus {
    background-color: #29a8df !important;
}

.dropdown-notifications-item:not(.mark-as-read) .dropdown-notifications-read span {
    background-color: #29a8df;
}

.bg-label-primary {
    background-color: #eee6ff !important;
    color: #29a8df !important;
}

.page-item.active .page-link,
.page-item.active .page-link:hover,
.page-item.active .page-link:focus,
.pagination li.active>a:not(.page-link),
.pagination li.active>a:not(.page-link):hover,
.pagination li.active>a:not(.page-link):focus {
    border-color: #29a8df;
    background-color: #29a8df;
    color: #fff;
}

.page-item.active .page-link.waves-effect .waves-ripple,
.pagination li.active>a:not(.page-link).waves-effect .waves-ripple {
    background: radial-gradient(#ffffff33 0,
            #ffffff4d 40%,
            #ffffff66 50%,
            #ffffff80 60%,
            #ffffff00 70%);
}

.pagination-outline-primary .page-item.active .page-link,
.pagination-outline-primary .page-item.active .page-link:hover,
.pagination-outline-primary .page-item.active .page-link:focus,
.pagination-outline-primary.pagination li.active>a:not(.page-link),
.pagination-outline-primary.pagination li.active>a:not(.page-link):hover,
.pagination-outline-primary.pagination li.active>a:not(.page-link):focus {
    border-color: #c8aafe;
    color: #29a8df;
    background-color: #f6f1ff;
}

.pagination-outline-primary .page-item.active .page-link.waves-effect .waves-ripple,
.pagination-outline-primary.pagination li.active>a:not(.page-link).waves-effect .waves-ripple {
    background: radial-gradient(#9055fd33 0,
            #9055fd4d 40%,
            #9055fd66 50%,
            #9055fd80 60%,
            #3a354100 70%);
}

.progress-bar {
    background-color: #29a8df;
}

.list-group-item-primary {
    border-color: #29a8df;
    background-color: #f2ebff;
    color: #29a8df !important;
}

a.list-group-item-primary,
button.list-group-item-primary {
    color: #29a8df;
}

a.list-group-item-primary:hover,
a.list-group-item-primary:focus,
button.list-group-item-primary:hover,
button.list-group-item-primary:focus {
    border-color: #29a8df;
    background-color: #e6dff2;
    color: #29a8df;
}

a.list-group-item-primary.active,
button.list-group-item-primary.active {
    border-color: #29a8df !important;
    background-color: #29a8df !important;
    color: #fff !important;
}

.list-group-item.active {
    background-color: #f6f1ff;
    color: #544f5a;
}

.list-group-item.active.waves-effect .waves-ripple {
    background: radial-gradient(rgba(144, 85, 253, 0.2) 0,
            rgba(144, 85, 253, 0.3) 40%,
            rgba(144, 85, 253, 0.4) 50%,
            rgba(144, 85, 253, 0.5) 60%,
            rgba(58, 53, 65, 0) 70%);
}

.alert-primary {
    background-color: #f2ebff;
    border-color: #f2ebff;
    color: #29a8df;
}

.alert-primary .btn-close {
    background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23891AB4'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>");
}

.alert-primary .alert-link {
    color: #29a8df;
}

.alert-primary hr {
    background-color: #29a8df !important;
}

.table-primary {
    --bs-table-bg: #e9ddff;
    --bs-table-striped-bg: #e0d5f6;
    --bs-table-striped-color: #3a3541;
    --bs-table-active-bg: #dbd0f0;
    --bs-table-active-color: #3a3541;
    --bs-table-hover-bg: #e2d6f7;
    --bs-table-hover-color: #3a3541;
    color: #3a3541;
    border-color: #d4c9e8;
}

.table-primary .btn-icon {
    color: #3a3541;
}

.btn-primary {
    color: #fff;
    background-color: #29a8df;
    border-color: #29a8df;
}

.btn-primary:hover {
    color: #000 !important;
    background-color: #fbb04d !important;
    border-color: #fbb04d !important;
}

/* .btn-primary {
    position: relative;
    color: #fff;
    background-color: #29a8df;
    border-color: #29a8df;
    overflow: hidden;
    z-index: 1;
    transition: color 0.4s ease;
}

.btn-primary::before,
.btn-primary::after {
    content: "";
    position: absolute;
    top: 0;
    width: 50%;
    height: 100%;
    background: #ff9900;
    z-index: -1;
    transition: transform 0.4s ease;
}

.btn-primary::before {
    left: 0;
    transform: translateX(-100%);
    transform-origin: left;
}

.btn-primary::after {
    right: 0;
    transform: translateX(100%);
    transform-origin: right;
}

.btn-primary:hover::before,
.btn-primary:hover::after {
    transform: translateX(0);
}

.btn-primary:hover {
    color: #000;
    border-color: #ff9900 !important;
} */

.btn-alter-primary {
    color: #000;
    background-color: #ff9900;
    border-color: #ff9900;
}

.btn-alter-primary:hover {
    color: #fff !important;
    background-color: #29a8df !important;
    border-color: #29a8df !important;
}

.btn-check:focus+.btn-primary,
.btn-primary:focus,
.btn-primary.focus {
    color: #000;
    background-color: #ff9900;
    border-color: #ff9900;
    box-shadow: none;
}

.btn-check:checked+.btn-primary,
.btn-check:active+.btn-primary,
.btn-primary:active,
.btn-primary.active,
.btn-primary.show.dropdown-toggle,
.show>.btn-primary.dropdown-toggle {
    color: #fff !important;
    background-color: #29a8df !important;
    border-color: #29a8df !important;
}

.btn-check:checked+.btn-primary:focus,
.btn-check:active+.btn-primary:focus,
.btn-primary:active:focus,
.btn-primary.active:focus,
.btn-primary.show.dropdown-toggle:focus,
.show>.btn-primary.dropdown-toggle:focus {
    box-shadow: none;
}

.btn-group .btn-primary,
.input-group .btn-primary {
    border-right: 1px solid #29a8df;
    border-left: 1px solid #29a8df;
}

.btn-group-vertical .btn-primary {
    border-top: 1px solid #29a8df;
    border-bottom: 1px solid #29a8df;
}

.btn-outline-primary {
    color: #29a8df;
    border-color: #c8aafe;
    background: transparent;
}

.btn-outline-primary.waves-effect .waves-ripple {
    background: radial-gradient(rgba(144, 85, 253, 0.2) 0,
            rgba(144, 85, 253, 0.3) 40%,
            rgba(144, 85, 253, 0.4) 50%,
            rgba(144, 85, 253, 0.5) 60%,
            rgba(255, 255, 255, 0) 70%);
}

.btn-outline-primary:hover {
    color: #29a8df !important;
    background-color: #f7f3ff !important;
    border-color: #c8aafe !important;
}

.btn-check:focus+.btn-outline-primary,
.btn-outline-primary:focus {
    color: #29a8df;
    background-color: #e4d6ff;
    border-color: #c8aafe;
}

.btn-check:checked+.btn-outline-primary,
.btn-check:active+.btn-outline-primary,
.btn-outline-primary:active,
.btn-outline-primary.active,
.btn-outline-primary.dropdown-toggle.show {
    color: #29a8df !important;
    background-color: #e0cffe !important;
    border-color: #c8aafe !important;
}

.btn-outline-primary .badge {
    background: #29a8df;
    border-color: #29a8df;
    color: #fff;
}

.btn-outline-primary:hover .badge,
.btn-outline-primary:focus:hover .badge,
.btn-outline-primary:active .badge,
.btn-outline-primary.active .badge,
.show>.btn-outline-primary.dropdown-toggle .badge {
    background: #29a8df;
    border-color: #29a8df;
    color: #fff;
}

.dropdown-item.waves-effect .waves-ripple {
    background: radial-gradient(rgba(144, 85, 253, 0.2) 0,
            rgba(144, 85, 253, 0.3) 40%,
            rgba(144, 85, 253, 0.4) 50%,
            rgba(144, 85, 253, 0.5) 60%,
            rgba(255, 255, 255, 0) 70%);
}

.dropdown-item:not(.disabled).active,
.dropdown-item:not(.disabled):active {
    background-color: rgba(144, 85, 253, 0.1);
    color: #29a8df !important;
}

.dropdown-menu>li:not(.disabled)>a:not(.dropdown-item):active,
.dropdown-menu>li.active:not(.disabled)>a:not(.dropdown-item) {
    background-color: rgba(144, 85, 253, 0.1);
    color: #29a8df !important;
}

.dropdown-menu>li:not(.disabled)>a:not(.dropdown-item):active.btn,
.dropdown-menu>li.active:not(.disabled)>a:not(.dropdown-item).btn {
    color: #fff !important;
}

.nav .nav-link:hover,
.nav .nav-link:focus {
    color: #29a8df;
}

.nav-pills .nav-link.active,
.nav-pills .nav-link.active:hover,
.nav-pills .nav-link.active:focus {
    background-color: #29a8df;
    color: #fff;
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-link.active:hover,
.nav-tabs .nav-link.active:focus {
    color: #29a8df;
}

.nav-tabs .nav-link.waves-effect .waves-ripple {
    background: radial-gradient(rgba(144, 85, 253, 0.2) 0,
            rgba(144, 85, 253, 0.3) 40%,
            rgba(144, 85, 253, 0.4) 50%,
            rgba(144, 85, 253, 0.5) 60%,
            rgba(255, 255, 255, 0) 70%);
}

.nav-tabs .tab-slider {
    background-color: #29a8df;
}

.form-control:focus,
.form-select:focus {
    border-color: #29a8df !important;
}

.form-floating-outline .form-control:focus,
.form-floating-outline .form-select:focus {
    border-color: #29a8df !important;
}

.input-group:not(.input-group-floating):focus-within .form-control,
.input-group:not(.input-group-floating):focus-within .input-group-text {
    border-color: #29a8df;
}

.form-check-input:focus {
    border-color: #29a8df;
}

.form-check-input:active {
    border-color: #29a8df;
}

.form-check-input:hover::after {
    background: rgba(58, 53, 65, 0.04);
}

.form-check-input:checked {
    background-color: #29a8df;
    border-color: #29a8df;
}

.form-check-input:checked::after {
    background: rgba(144, 85, 253, 0.08) !important;
}

.form-check-input[type="checkbox"]:indeterminate {
    background-color: #29a8df;
    border-color: #29a8df;
}

.form-switch .form-check-input:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23891AB4'/%3e%3c/svg%3e");
}

.form-switch .form-check-input:checked {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.form-control:focus~.form-label {
    border-color: #29a8df;
}

.form-control:focus~.form-label::after {
    border-color: inherit;
}

.form-range::-webkit-slider-thumb {
    background-color: #29a8df;
}

.form-range::-webkit-slider-thumb:hover {
    box-shadow: 0 0 0 8px rgba(144, 85, 253, 0.15),
        0px 3px 14px 0px rgba(58, 53, 65, 0.14);
}

.form-range::-webkit-slider-thumb:active {
    background-color: #29a8df;
    box-shadow: 0 0 0 10px rgba(144, 85, 253, 0.2),
        0px 3px 14px 0px rgba(58, 53, 65, 0.14);
}

.form-range::-moz-range-thumb:hover {
    box-shadow: 0 0 0 8px rgba(144, 85, 253, 0.15),
        0px 3px 14px 0px rgba(58, 53, 65, 0.14);
}

.form-range::-moz-range-thumb:active {
    box-shadow: 0 0 0 10px rgba(144, 85, 253, 0.2),
        0px 3px 14px 0px rgba(58, 53, 65, 0.14);
}

.form-range::-webkit-slider-runnable-track {
    background-color: #29a8df;
}

.form-range::-moz-range-track {
    background-color: #29a8df;
}

.divider.divider-primary .divider-text:before,
.divider.divider-primary .divider-text:after {
    border-color: #29a8df;
}

.navbar.bg-primary {
    color: #e8dbff;
}

.navbar.bg-primary .navbar-brand,
.navbar.bg-primary .navbar-brand a {
    color: #fff;
}

.navbar.bg-primary .navbar-brand:hover,
.navbar.bg-primary .navbar-brand:focus,
.navbar.bg-primary .navbar-brand a:hover,
.navbar.bg-primary .navbar-brand a:focus {
    color: #fff;
}

.navbar.bg-primary .navbar-search-wrapper .navbar-search-icon,
.navbar.bg-primary .navbar-search-wrapper .search-input {
    color: #e8dbff;
}

.navbar.bg-primary .search-input-wrapper .search-input,
.navbar.bg-primary .search-input-wrapper .search-toggler {
    color: #e8dbff;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.navbar.bg-primary .navbar-nav>.nav-link,
.navbar.bg-primary .navbar-nav>.nav-item>.nav-link,
.navbar.bg-primary .navbar-nav>.nav>.nav-item>.nav-link {
    color: #e8dbff;
}

.navbar.bg-primary .navbar-nav>.nav-link:hover,
.navbar.bg-primary .navbar-nav>.nav-link:focus,
.navbar.bg-primary .navbar-nav>.nav-item>.nav-link:hover,
.navbar.bg-primary .navbar-nav>.nav-item>.nav-link:focus,
.navbar.bg-primary .navbar-nav>.nav>.nav-item>.nav-link:hover,
.navbar.bg-primary .navbar-nav>.nav>.nav-item>.nav-link:focus {
    color: #fff;
}

.navbar.bg-primary .navbar-nav>.nav-link.disabled,
.navbar.bg-primary .navbar-nav>.nav-item>.nav-link.disabled,
.navbar.bg-primary .navbar-nav>.nav>.nav-item>.nav-link.disabled {
    color: #c5a5fe !important;
}

.navbar.bg-primary .navbar-nav .show>.nav-link,
.navbar.bg-primary .navbar-nav .active>.nav-link,
.navbar.bg-primary .navbar-nav .nav-link.show,
.navbar.bg-primary .navbar-nav .nav-link.active {
    color: #fff;
}

.navbar.bg-primary .navbar-toggler {
    color: #e8dbff;
    border-color: rgba(255, 255, 255, 0.15);
}

.navbar.bg-primary .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='rgba(255, 255, 255, 0.8)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>");
}

.navbar.bg-primary .navbar-text {
    color: #e8dbff;
}

.navbar.bg-primary .navbar-text a {
    color: #fff;
}

.navbar.bg-primary .navbar-text a:hover,
.navbar.bg-primary .navbar-text a:focus {
    color: #fff;
}

.navbar.bg-primary hr {
    border-color: rgba(255, 255, 255, 0.15);
}

.menu.bg-primary {
    background-color: #29a8df !important;
    color: #e8dbff;
}

.menu.bg-primary .menu-link,
.menu.bg-primary .menu-horizontal-prev,
.menu.bg-primary .menu-horizontal-next {
    color: #e8dbff;
}

.menu.bg-primary .menu-link:hover,
.menu.bg-primary .menu-link:focus,
.menu.bg-primary .menu-horizontal-prev:hover,
.menu.bg-primary .menu-horizontal-prev:focus,
.menu.bg-primary .menu-horizontal-next:hover,
.menu.bg-primary .menu-horizontal-next:focus {
    color: #fff;
}

.menu.bg-primary .menu-link.active,
.menu.bg-primary .menu-horizontal-prev.active,
.menu.bg-primary .menu-horizontal-next.active {
    color: #fff;
}

.menu.bg-primary .menu-item.disabled .menu-link,
.menu.bg-primary .menu-horizontal-prev.disabled,
.menu.bg-primary .menu-horizontal-next.disabled {
    color: #c5a5fe !important;
}

.menu.bg-primary .menu-item.active:not(.open)>.menu-link:not(.menu-toggle),
.menu.bg-primary .menu-item.active:not(.open)>.menu-link:not(.menu-toggle)::before {
    color: #fff !important;
    border-color: #fff !important;
}

.menu.bg-primary .menu-item.active>.menu-link:not(.menu-toggle) {
    background: linear-gradient(270deg, #9359fd 0%, #c6a7fe 100%);
}

.menu.bg-primary.menu-horizontal .menu-sub>.menu-item.active>.menu-link:not(.menu-toggle),
.menu.bg-primary.menu-horizontal .menu-sub>.menu-item.active>.menu-link:not(.menu-toggle)::before {
    background: #f2ebff;
    color: #9359fd !important;
}

.menu.bg-primary.menu-horizontal .menu-inner>.menu-item.active>.menu-link.menu-toggle {
    background: linear-gradient(270deg, #9359fd 0%, #c6a7fe 100%);
}

.menu.bg-primary.menu-horizontal .menu-inner>.menu-item.active>.menu-link.menu-toggle,
.menu.bg-primary.menu-horizontal .menu-inner>.menu-item.active>.menu-link.menu-toggle:after {
    color: #fff;
}

.menu.bg-primary .menu-inner-shadow {
    background: linear-gradient(#29a8df 5%,
            rgba(144, 85, 253, 0.75) 45%,
            rgba(144, 85, 253, 0.2) 80%,
            transparent);
}

.menu.bg-primary .menu-text {
    color: #fff;
}

.menu.bg-primary .menu-header {
    color: #d2baff;
}

.menu.bg-primary hr,
.menu.bg-primary .menu-divider,
.menu.bg-primary .menu-inner>.menu-item.open>.menu-sub::before {
    border-color: #ffffff26 !important;
}

.menu.bg-primary .menu-block::before {
    background-color: #d2baff;
}

.menu.bg-primary .ps__thumb-y,
.menu.bg-primary .ps__rail-y.ps--clicking>.ps__thumb-y {
    background: #ffffff97 !important;
}

.footer.bg-primary {
    color: #e8dbff;
}

.footer.bg-primary .footer-link {
    color: #e8dbff;
}

.footer.bg-primary .footer-link:hover,
.footer.bg-primary .footer-link:focus {
    color: #e8dbff;
}

.footer.bg-primary .footer-link.disabled {
    color: #c5a5fe !important;
}

.footer.bg-primary .footer-text {
    color: #fff;
}

.footer.bg-primary .show>.footer-link,
.footer.bg-primary .active>.footer-link,
.footer.bg-primary .footer-link.show,
.footer.bg-primary .footer-link.active {
    color: #fff;
}

.footer.bg-primary hr {
    border-color: rgba(255, 255, 255, 0.15);
}

.form-floating>.form-control:focus~label,
.form-floating>.form-control:focus:not(:placeholder-shown)~label,
.form-floating>.form-select:focus~label,
.form-floating>.form-select:focus:not(:placeholder-shown)~label {
    color: #29a8df;
}

.form-floating-outline :not(select):focus+label,
.form-floating-outline :not(select):focus+span {
    color: #29a8df;
}

.form-floating-outline label::after,
.form-floating-outline>span::after {
    background: #fff;
}

.form-floating-outline label.bg-body::after,
.form-floating-outline>span.bg-body::after {
    background: #f4f5fa !important;
}

.svg-illustration svg {
    fill: #29a8df;
}

html:not([dir="rtl"]) .border-primary,
html[dir="rtl"] .border-primary {
    border-color: #29a8df !important;
}

a {
    color: #29a8df;
}

a:hover {
    color: #a000df;
}

.fill-primary {
    fill: #29a8df;
}

.layout-navbar-fixed .window-scrolled .bg-navbar-theme.layout-navbar,
.layout-horizontal .bg-navbar-theme.layout-navbar,
.bg-menu-theme.menu-horizontal {
    background-color: rgba(255, 255, 255, 100%) !important;
}

.bg-navbar-theme {
    color: #544f5a;
}

.bg-navbar-theme .navbar-brand,
.bg-navbar-theme .navbar-brand a {
    color: #544f5a;
}

.bg-navbar-theme .navbar-brand:hover,
.bg-navbar-theme .navbar-brand:focus,
.bg-navbar-theme .navbar-brand a:hover,
.bg-navbar-theme .navbar-brand a:focus {
    color: #544f5a;
}

.bg-navbar-theme .navbar-search-wrapper .navbar-search-icon,
.bg-navbar-theme .navbar-search-wrapper .search-input {
    color: #544f5a;
}

.bg-navbar-theme .search-input-wrapper .search-input,
.bg-navbar-theme .search-input-wrapper .search-toggler {
    color: #544f5a;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.bg-navbar-theme .navbar-nav>.nav-link,
.bg-navbar-theme .navbar-nav>.nav-item>.nav-link,
.bg-navbar-theme .navbar-nav>.nav>.nav-item>.nav-link {
    color: #544f5a;
}

.bg-navbar-theme .navbar-nav>.nav-link:hover,
.bg-navbar-theme .navbar-nav>.nav-link:focus,
.bg-navbar-theme .navbar-nav>.nav-item>.nav-link:hover,
.bg-navbar-theme .navbar-nav>.nav-item>.nav-link:focus,
.bg-navbar-theme .navbar-nav>.nav>.nav-item>.nav-link:hover,
.bg-navbar-theme .navbar-nav>.nav>.nav-item>.nav-link:focus {
    color: #544f5a;
}

.bg-navbar-theme .navbar-nav>.nav-link.disabled,
.bg-navbar-theme .navbar-nav>.nav-item>.nav-link.disabled,
.bg-navbar-theme .navbar-nav>.nav>.nav-item>.nav-link.disabled {
    color: #94919a !important;
}

.bg-navbar-theme .navbar-nav .show>.nav-link,
.bg-navbar-theme .navbar-nav .active>.nav-link,
.bg-navbar-theme .navbar-nav .nav-link.show,
.bg-navbar-theme .navbar-nav .nav-link.active {
    color: #544f5a;
}

.bg-navbar-theme .navbar-toggler {
    color: #544f5a;
    border-color: #544f5a14;
}

.bg-navbar-theme .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='rgba(137, 134, 141, 0.75)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>");
}

.bg-navbar-theme .navbar-text {
    color: #544f5a;
}

.bg-navbar-theme .navbar-text a {
    color: #544f5a;
}

.bg-navbar-theme .navbar-text a:hover,
.bg-navbar-theme .navbar-text a:focus {
    color: #544f5a;
}

.bg-navbar-theme hr {
    border-color: #544f5a14;
}

.bg-menu-theme {
    background-color: #f4f5fa !important;
    color: #000000;
    box-shadow: 0 0.625rem 0.875rem #3a35411f;
}

.bg-menu-theme .menu-link,
.bg-menu-theme .menu-horizontal-prev,
.bg-menu-theme .menu-horizontal-next {
    color: #5a5a5a;
}

.bg-menu-theme .menu-link:hover,
.bg-menu-theme .menu-link:focus,
.bg-menu-theme .menu-horizontal-prev:hover,
.bg-menu-theme .menu-horizontal-prev:focus,
.bg-menu-theme .menu-horizontal-next:hover,
.bg-menu-theme .menu-horizontal-next:focus {
    color: #5a5a5a;
}

.bg-menu-theme .menu-link.active,
.bg-menu-theme .menu-horizontal-prev.active,
.bg-menu-theme .menu-horizontal-next.active {
    color: #5a5a5a;
}

.bg-menu-theme .menu-item.disabled .menu-link,
.bg-menu-theme .menu-horizontal-prev.disabled,
.bg-menu-theme .menu-horizontal-next.disabled {
    color: #94919a !important;
}

.bg-menu-theme .menu-item.active:not(.open)>.menu-link:not(.menu-toggle),
.bg-menu-theme .menu-item.active:not(.open)>.menu-link:not(.menu-toggle)::before {
    color: #fff !important;
    border-color: #fff !important;
}

.bg-menu-theme .menu-item.active>.menu-link:not(.menu-toggle) {
    /* background: linear-gradient(270deg, #29a8df 0%, #fbb04d 100%); */
    background: #29a8df;
}

/* .bg-menu-theme.menu-horizontal {
    background-color: rgba(255, 255, 255, 100%) !important;
} */

.bg-menu-theme.menu-horizontal .menu-sub>.menu-item.active>.menu-link:not(.menu-toggle),
.bg-menu-theme.menu-horizontal .menu-sub>.menu-item.active>.menu-link:not(.menu-toggle)::before {
    /* background: #e2e2e2; */
    background-color: rgba(46, 38, 61, 0.08);
    color: #29a8df !important;
}

.bg-menu-theme.menu-horizontal .menu-inner>.menu-item.active>.menu-link.menu-toggle {
    background: #29a8df;
}

.bg-menu-theme.menu-horizontal .menu-inner>.menu-item.active>.menu-link.menu-toggle,
.bg-menu-theme.menu-horizontal .menu-inner>.menu-item.active>.menu-link.menu-toggle:after {
    color: #fff;
}

.bg-menu-theme .menu-inner-shadow {
    background: linear-gradient(#f4f5fa 5%,
            rgba(244, 245, 250, 0.75) 45%,
            rgba(244, 245, 250, 0.2) 80%,
            transparent);
}

.bg-menu-theme .menu-text {
    color: #544f5a;
}

.bg-menu-theme .menu-header {
    color: #7c7982;
}

.bg-menu-theme hr,
.bg-menu-theme .menu-divider,
.bg-menu-theme .menu-inner>.menu-item.open>.menu-sub::before {
    border-color: transparent !important;
}

.bg-menu-theme .menu-block::before {
    background-color: #7c7982;
}

.bg-menu-theme .ps__thumb-y,
.bg-menu-theme .ps__rail-y.ps--clicking>.ps__thumb-y {
    background: #544f5a37 !important;
}

@media (min-width: 1200px) {
    .layout-menu-collapsed.layout-menu-hover .bg-menu-theme {
        box-shadow: 0 0.625rem 0.875rem #3a35411f;
    }
}

.bg-footer-theme {
    color: #29a8df;
}

.bg-footer-theme .footer-link {
    color: #29a8df;
}

.bg-footer-theme .footer-link:hover,
.bg-footer-theme .footer-link:focus {
    color: #29a8df;
}

.bg-footer-theme .footer-link.disabled {
    color: #b895fc !important;
}

.bg-footer-theme .footer-text {
    color: #544f5a;
}

.bg-footer-theme .show>.footer-link,
.bg-footer-theme .active>.footer-link,
.bg-footer-theme .footer-link.show,
.bg-footer-theme .footer-link.active {
    color: #544f5a;
}

.bg-footer-theme hr {
    border-color: #423d4714;
}
